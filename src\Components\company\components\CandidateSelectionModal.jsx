import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  XMarkIcon,
  MagnifyingGlassIcon,
  UserIcon,
  CheckIcon,
  FunnelIcon,
  UserGroupIcon,
  BriefcaseIcon,
  AcademicCapIcon
} from '@heroicons/react/24/outline';
import useCompanyStore from '../../../store/companyStore';
import toast from 'react-hot-toast';

const CandidateSelectionModal = ({ 
  isOpen, 
  onClose, 
  onAssign, 
  testId,
  alreadyAssignedCandidates = [] 
}) => {
  const { 
    candidates, 
    getCandidates, 
    searchCandidates, 
    getAvailableCandidatesForTest,
    loading 
  } = useCompanyStore();

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCandidates, setSelectedCandidates] = useState([]);
  const [filteredCandidates, setFilteredCandidates] = useState([]);
  const [filters, setFilters] = useState({
    experience: '',
    skills: '',
    location: '',
    status: 'available'
  });
  const [showFilters, setShowFilters] = useState(false);
  const [isAssigning, setIsAssigning] = useState(false);

  useEffect(() => {
    if (isOpen) {
      if (testId) {
        fetchAvailableCandidates();
      } else {
        fetchAllCandidates();
      }
    }
  }, [isOpen, testId]);

  useEffect(() => {
    handleSearch();
  }, [searchTerm, candidates, filters]);

  const fetchAllCandidates = async () => {
    await getCandidates();
  };

  const fetchAvailableCandidates = async () => {
    const result = await getAvailableCandidatesForTest(testId);
    // Handle both old format (array) and new format (object with candidates property)
    const availableCandidates = Array.isArray(result) ? result : (result?.candidates || []);

    // Debug logging to verify API response structure
    console.log('API response from getAvailableCandidatesForTest:', result);
    console.log('Processed available candidates:', availableCandidates);

    setFilteredCandidates(availableCandidates);
  };

  const handleSearch = async () => {
    if (searchTerm.trim() || Object.values(filters).some(f => f)) {
      const result = await searchCandidates(searchTerm, filters);
      // Handle both old format (array) and new format (object with candidates property)
      const searchResults = Array.isArray(result) ? result : (result?.candidates || []);
      setFilteredCandidates(searchResults);
    } else {
      setFilteredCandidates(candidates);
    }
  };

  const handleCandidateSelect = (candidate) => {
    setSelectedCandidates(prev => {
      // Use candidateId if available (from available-for-test API), otherwise use _id
      const candidateId = candidate.candidateId || candidate._id;
      const isSelected = prev.some(c => (c.candidateId || c._id) === candidateId);
      if (isSelected) {
        return prev.filter(c => (c.candidateId || c._id) !== candidateId);
      } else {
        return [...prev, candidate];
      }
    });
  };

  const handleSelectAll = () => {
    const availableCandidates = filteredCandidates.filter(
      candidate => {
        const candidateId = candidate.candidateId || candidate._id;
        return !alreadyAssignedCandidates.includes(candidateId);
      }
    );

    if (selectedCandidates.length === availableCandidates.length) {
      setSelectedCandidates([]);
    } else {
      setSelectedCandidates(availableCandidates);
    }
  };

  const handleAssign = async () => {
    if (selectedCandidates.length === 0) {
      toast.error('Please select at least one candidate');
      return;
    }

    setIsAssigning(true);
    try {
      // Use candidateId if available (from available-for-test API), otherwise use _id
      const candidateIds = selectedCandidates.map(c => c.candidateId || c._id);

      // Debug logging to verify we're using correct IDs
      console.log('Selected candidates:', selectedCandidates);
      console.log('Candidate IDs being sent:', candidateIds);

      await onAssign(candidateIds);
      setSelectedCandidates([]);
      onClose();
    } catch (error) {
      console.error('Assignment error:', error);
      toast.error('Failed to assign candidates');
    } finally {
      setIsAssigning(false);
    }
  };

  const isCandidateSelected = (candidate) => {
    const candidateId = candidate.candidateId || candidate._id;
    return selectedCandidates.some(c => (c.candidateId || c._id) === candidateId);
  };

  const isCandidateAlreadyAssigned = (candidate) => {
    const candidateId = candidate.candidateId || candidate._id;
    return alreadyAssignedCandidates.includes(candidateId);
  };

  const availableCandidates = filteredCandidates.filter(
    candidate => {
      const candidateId = candidate.candidateId || candidate._id;
      return !alreadyAssignedCandidates.includes(candidateId);
    }
  );

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-white rounded-2xl shadow-2xl w-full max-w-5xl max-h-[90vh] overflow-hidden"
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-purple-600 to-purple-700 text-white p-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold mb-2">Select Candidates</h2>
                <p className="text-purple-100">Choose candidates to assign to this test</p>
              </div>
              <button
                onClick={onClose}
                className="p-2 hover:bg-white/20 rounded-lg transition-colors"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
          </div>

          {/* Search and Filters */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex flex-col md:flex-row gap-4 items-center">
              <div className="flex-1 relative">
                <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search candidates by name, email, or skills..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
              
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2 px-4 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors"
              >
                <FunnelIcon className="h-5 w-5" />
                Filters
              </button>
            </div>

            {/* Advanced Filters */}
            {showFilters && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4"
              >
                <select
                  value={filters.experience}
                  onChange={(e) => setFilters(prev => ({ ...prev, experience: e.target.value }))}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  <option value="">All Experience</option>
                  <option value="0-1">0-1 years</option>
                  <option value="1-3">1-3 years</option>
                  <option value="3-5">3-5 years</option>
                  <option value="5+">5+ years</option>
                </select>

                <input
                  type="text"
                  placeholder="Skills (e.g., React, Python)"
                  value={filters.skills}
                  onChange={(e) => setFilters(prev => ({ ...prev, skills: e.target.value }))}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />

                <input
                  type="text"
                  placeholder="Location"
                  value={filters.location}
                  onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value }))}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />

                <select
                  value={filters.status}
                  onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  <option value="">All Status</option>
                  <option value="available">Available</option>
                  <option value="interviewing">Interviewing</option>
                  <option value="hired">Hired</option>
                </select>
              </motion.div>
            )}
          </div>

          {/* Selection Summary */}
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <span className="text-sm text-gray-600">
                  {availableCandidates.length} candidates available
                </span>
                <span className="text-sm font-medium text-purple-600">
                  {selectedCandidates.length} selected
                </span>
              </div>
              
              <button
                onClick={handleSelectAll}
                className="text-sm text-purple-600 hover:text-purple-800 font-medium"
              >
                {selectedCandidates.length === availableCandidates.length ? 'Deselect All' : 'Select All'}
              </button>
            </div>
          </div>

          {/* Candidates List */}
          <div className="p-6 overflow-y-auto max-h-[50vh]">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-2 border-purple-600 border-t-transparent"></div>
                <span className="ml-3 text-gray-600">Loading candidates...</span>
              </div>
            ) : availableCandidates.length === 0 ? (
              <div className="text-center py-12">
                <UserGroupIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-600 mb-2">No Candidates Available</h3>
                <p className="text-gray-500">
                  {filteredCandidates.length === 0 
                    ? 'No candidates match your search criteria'
                    : 'All candidates are already assigned to this test'
                  }
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {availableCandidates.map((candidate, index) => {
                  const isSelected = isCandidateSelected(candidate);
                  const candidateId = candidate.candidateId || candidate._id;

                  return (
                    <motion.div
                      key={candidateId}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.05 }}
                      onClick={() => handleCandidateSelect(candidate)}
                      className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                        isSelected
                          ? 'border-purple-500 bg-purple-50'
                          : 'border-gray-200 hover:border-purple-300 hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex items-start gap-3">
                        <div className={`w-6 h-6 rounded border-2 flex items-center justify-center ${
                          isSelected
                            ? 'bg-purple-600 border-purple-600'
                            : 'border-gray-300'
                        }`}>
                          {isSelected && <CheckIcon className="h-4 w-4 text-white" />}
                        </div>
                        
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <UserIcon className="h-5 w-5 text-gray-400" />
                            <h4 className="font-semibold text-gray-800">
                              {candidate.name || candidate.firstName + ' ' + candidate.lastName}
                            </h4>
                          </div>
                          
                          <p className="text-sm text-gray-600 mb-2">{candidate.email}</p>
                          
                          {candidate.skills && (
                            <div className="flex items-center gap-1 mb-2">
                              <AcademicCapIcon className="h-4 w-4 text-gray-400" />
                              <span className="text-xs text-gray-500">
                                {candidate.skills.slice(0, 3).join(', ')}
                                {candidate.skills.length > 3 && ` +${candidate.skills.length - 3} more`}
                              </span>
                            </div>
                          )}
                          
                          {candidate.experience && (
                            <div className="flex items-center gap-1">
                              <BriefcaseIcon className="h-4 w-4 text-gray-400" />
                              <span className="text-xs text-gray-500">
                                {candidate.experience} years experience
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-6 py-4 flex items-center justify-between border-t border-gray-200">
            <div className="text-sm text-gray-600">
              {selectedCandidates.length} candidate(s) selected
            </div>
            
            <div className="flex items-center gap-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-200 rounded-lg transition-colors"
              >
                Cancel
              </button>
              
              <button
                onClick={handleAssign}
                disabled={selectedCandidates.length === 0 || isAssigning}
                className="flex items-center gap-2 px-6 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors font-medium"
              >
                {isAssigning ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    Assigning...
                  </>
                ) : (
                  <>
                    <UserGroupIcon className="h-4 w-4" />
                    Assign {selectedCandidates.length} Candidate(s)
                  </>
                )}
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default CandidateSelectionModal;
