import { useState, useEffect, useCallback } from 'react';
import * as XLSX from 'xlsx';
import useCompanyStore from '../../../store/companyStore';

const useQuestionManager = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Get store methods and state
  const {
    questions,
    createQuestion,
    getQuestions,
    updateQuestion,
    deleteQuestion: deleteQuestionAPI,
    uploadQuestionsFromExcel,
    error: storeError,
    clearError
  } = useCompanyStore();

  // Load questions on mount
  useEffect(() => {
    const loadQuestions = async () => {
      setLoading(true);
      try {
        await getQuestions();
      } catch (err) {
        setError('Failed to load questions');
      } finally {
        setLoading(false);
      }
    };
    
    loadQuestions();
  }, [getQuestions]);

  // Sync store error with local error
  useEffect(() => {
    if (storeError) {
      setError(storeError);
    }
  }, [storeError]);

  // Add a single question
  const addQuestion = useCallback(async (questionData) => {
    try {
      setLoading(true);
      setError(null);

      // Send data directly as it matches the backend schema
      const result = await createQuestion(questionData);

      if (result) {
        // Refresh questions list
        await getQuestions();
        return { success: true, question: result };
      } else {
        return { success: false, error: 'Failed to create question' };
      }
    } catch (err) {
      setError('Failed to add question');
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  }, [createQuestion, getQuestions]);

  // Update a question
  const updateQuestionData = useCallback(async (questionId, updates) => {
    try {
      setLoading(true);
      setError(null);

      // Send updates directly as they match the backend schema
      const result = await updateQuestion(questionId, updates);

      if (result) {
        // Refresh questions list
        await getQuestions();
        return { success: true };
      } else {
        return { success: false, error: 'Failed to update question' };
      }
    } catch (err) {
      setError('Failed to update question');
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  }, [updateQuestion, getQuestions]);

  // Delete a question
  const deleteQuestion = useCallback(async (questionId) => {
    try {
      setLoading(true);
      setError(null);
      
      await deleteQuestionAPI(questionId);
      // Refresh questions list
      await getQuestions();
      return { success: true };
    } catch (err) {
      setError('Failed to delete question');
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  }, [deleteQuestionAPI, getQuestions]);

  // Delete multiple questions
  const deleteQuestions = useCallback(async (questionIds) => {
    try {
      setLoading(true);
      setError(null);
      
      // Delete questions one by one
      for (const id of questionIds) {
        await deleteQuestionAPI(id);
      }
      
      // Refresh questions list
      await getQuestions();
      return { success: true };
    } catch (err) {
      setError('Failed to delete questions');
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  }, [deleteQuestionAPI, getQuestions]);

  // Upload questions from Excel file
  const uploadQuestionsFromFile = useCallback(async (file) => {
    try {
      setLoading(true);
      setError(null);

      const result = await uploadQuestionsFromExcel(file);

      if (result.success) {
        // Refresh questions list after successful upload
        await getQuestions();

        return {
          success: true,
          count: result.summary?.questionsInserted || 0,
          message: result.message,
          summary: result.summary
        };
      } else {
        setError(result.error);
        return {
          success: false,
          error: result.error,
          details: result.details,
          validationErrors: result.validationErrors,
          expectedFormat: result.expectedFormat
        };
      }
    } catch (err) {
      setError('Failed to upload Excel file');
      return {
        success: false,
        error: err.message || 'Failed to upload Excel file'
      };
    } finally {
      setLoading(false);
    }
  }, [uploadQuestionsFromExcel, getQuestions]);

  // Get questions for a specific job (now filtering by category)
  const getQuestionsForJob = useCallback((jobTitle) => {
    if (!jobTitle || !questions) return [];
    // Since we removed job association, return questions by category
    // You might want to filter by category that matches the job type
    return questions.filter(q =>
      q.category && q.category.toLowerCase().includes(jobTitle.toLowerCase()) ||
      q.questionText && q.questionText.toLowerCase().includes(jobTitle.toLowerCase())
    );
  }, [questions]);

  // Search questions
  const searchQuestions = useCallback((searchTerm, category = null) => {
    let filteredQuestions = category
      ? questions.filter(q => q.category === category)
      : questions || [];

    if (!searchTerm) return filteredQuestions;

    const term = searchTerm.toLowerCase();
    return filteredQuestions.filter(q => {
      // Search in question text
      if (q.questionText?.toLowerCase().includes(term)) return true;

      // Search in options for MCQ/Multiple-Select
      if (q.options && Array.isArray(q.options)) {
        return q.options.some(opt => opt.text?.toLowerCase().includes(term));
      }

      // Search in correct answer
      if (q.correctAnswer?.toLowerCase().includes(term)) return true;

      // Search in explanation
      if (q.explanation?.toLowerCase().includes(term)) return true;

      // Search in category
      if (q.category?.toLowerCase().includes(term)) return true;

      return false;
    });
  }, [questions]);

  // Export questions to Excel
  const exportQuestions = useCallback((category = null) => {
    try {
      const questionsToExport = category
        ? questions.filter(q => q.category === category)
        : questions || [];

      if (questionsToExport.length === 0) {
        return { success: false, error: 'No questions to export' };
      }

      const exportData = questionsToExport.map(q => {
        const baseData = {
          questionText: q.questionText,
          questionType: q.questionType,
          category: q.category,
          difficulty: q.difficulty,
          points: q.points,
          explanation: q.explanation || ''
        };

        // Add options for MCQ/Multiple-Select
        if (q.options && Array.isArray(q.options)) {
          q.options.forEach((option, index) => {
            baseData[`option${index + 1}`] = option.text;
            if (option.isCorrect) {
              baseData.correctAnswer = option.text;
            }
          });
        } else if (q.correctAnswer) {
          baseData.correctAnswer = q.correctAnswer;
        }

        return baseData;
      });

      const worksheet = XLSX.utils.json_to_sheet(exportData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Questions');

      const fileName = category
        ? `${category}_questions.xlsx`
        : 'all_questions.xlsx';

      XLSX.writeFile(workbook, fileName);
      return { success: true };
    } catch (err) {
      return { success: false, error: 'Failed to export questions' };
    }
  }, [questions]);

  // Clear error
  const clearErrorState = useCallback(() => {
    setError(null);
    clearError();
  }, [clearError]);

  return {
    questions: questions || [],
    loading,
    error,
    addQuestion,
    updateQuestion: updateQuestionData,
    deleteQuestion,
    deleteQuestions,
    uploadQuestionsFromFile,
    getQuestionsForJob,
    searchQuestions,
    exportQuestions,
    setError: clearErrorState
  };
};

export default useQuestionManager;
