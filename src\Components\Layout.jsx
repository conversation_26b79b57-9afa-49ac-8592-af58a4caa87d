import React from "react";
import { Outlet, NavLink, useLocation, useNavigate } from "react-router-dom";
import { BookOpenCheckIcon, House, Plus, TestTubeDiagonalIcon, TvMinimalPlayIcon, UserRoundPenIcon, ChevronRight, ArrowLeft, Menu, X } from "lucide-react";

const Layout = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [sidebarOpen, setSidebarOpen] = React.useState(false);

  // Define pages that should hide the sidebar
  const hideSidebar = location.pathname === "/interview" || location.pathname === "/test";

  // Define page titles and breadcrumbs
  const getPageInfo = () => {
    const path = location.pathname;
    switch (path) {
      case "/dashboard":
        return { title: "Dashboard", breadcrumbs: ["Home", "Dashboard"] };
      case "/job-create":
        return { title: "Create Job", breadcrumbs: ["Home", "Job Management", "Create Job"] };
      case "/aptitude":
        return { title: "Add Questions", breadcrumbs: ["Home", "Question Bank", "Add Questions"] };
      case "/test-management":
        return { title: "Test Management", breadcrumbs: ["Home", "Tests", "Test Management"] };
      case "/profile":
        return { title: "Profile", breadcrumbs: ["Home", "Account", "Profile"] };
      case "/test":
        return { title: "Take Test", breadcrumbs: ["Home", "Tests", "Take Test"] };
      case "/interview":
        return { title: "Interview", breadcrumbs: ["Home", "Interview"] };
      default:
        return { title: "Proyuj", breadcrumbs: ["Home"] };
    }
  };

  const pageInfo = getPageInfo();

  // Navigation items configuration
  const navigationItems = [
    {
      to: "/dashboard",
      icon: House,
      label: "Dashboard",
      description: "Overview and analytics"
    },
    {
      to: "/job-create",
      icon: Plus,
      label: "Create Job",
      description: "Post new job openings"
    },
    {
      to: "/aptitude",
      icon: BookOpenCheckIcon,
      label: "Add Questions",
      description: "Manage question bank"
    },
    {
      to: "/test-management",
      icon: TestTubeDiagonalIcon,
      label: "Test Management",
      description: "Create and manage tests"
    },
    {
      to: "/profile",
      icon: UserRoundPenIcon,
      label: "Profile",
      description: "Account settings"
    }
  ];

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Enhanced Top Bar */}
      <header className="flex-shrink-0 fixed top-0 left-0 w-full z-50 shadow-lg h-16 flex items-center border-b border-gray-200" style={{ background: 'rgb(35, 65, 75)' }}>
        <div className="flex items-center justify-between w-full px-4 lg:px-8">
          <div className="flex items-center gap-4">
            {/* Mobile menu button */}
            {!hideSidebar && (
              <button
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="lg:hidden p-2 rounded-lg text-white hover:bg-white/10 transition-colors"
              >
                {sidebarOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
              </button>
            )}

            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-white/10 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <circle cx="12" cy="12" r="10" />
                  <path d="M12 8v4l3 3" />
                </svg>
              </div>
              <h1 className="text-2xl font-bold text-white">Proyuj</h1>
            </div>
          </div>

          {/* Page title for mobile */}
          <div className="hidden sm:block">
            <h2 className="text-lg font-semibold text-white/90">{pageInfo.title}</h2>
          </div>

          {/* User actions */}
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
              <UserRoundPenIcon className="w-4 h-4 text-white" />
            </div>
          </div>
        </div>
      </header>

      <div className="flex flex-1 pt-16 overflow-hidden">
        {/* Enhanced Sidebar */}
        {!hideSidebar && (
          <>
            {/* Mobile overlay */}
            {sidebarOpen && (
              <div
                className="fixed inset-0 bg-black/50 z-40 lg:hidden"
                onClick={() => setSidebarOpen(false)}
              />
            )}

            <aside className={`
              ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
              lg:translate-x-0 fixed lg:static inset-y-0 left-0 z-40 w-72 bg-gradient-to-b from-[rgb(35,65,75)] to-gray-900 shadow-2xl flex-shrink-0 overflow-y-auto transition-transform duration-300 ease-in-out
            `}>
              {/* Sidebar Header */}
              <div className="px-6 py-6 border-b border-gray-800">
                <div className="flex items-center gap-3 text-white">
                  <div className="w-10 h-10 bg-white/10 rounded-xl flex items-center justify-center">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <circle cx="12" cy="12" r="10" />
                      <path d="M12 8v4l3 3" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold tracking-tight">Navigation</h3>
                    <p className="text-sm text-gray-300">Company Portal</p>
                  </div>
                </div>
              </div>

              {/* Navigation */}
              <nav className="flex flex-col gap-2 px-4 py-6">
                {navigationItems.map((item) => {
                  const Icon = item.icon;
                  return (
                    <NavLink
                      key={item.to}
                      to={item.to}
                      onClick={() => setSidebarOpen(false)}
                      className={({ isActive }) =>
                        isActive
                          ? "flex items-center gap-4 text-white bg-white/15 shadow-lg ring-2 ring-white/20 rounded-xl px-4 py-4 transition-all duration-200 group"
                          : "flex items-center gap-4 text-gray-200 hover:bg-white/5 hover:text-white rounded-xl px-4 py-4 transition-all duration-200 group"
                      }
                    >
                      <div className="flex-shrink-0 w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center group-hover:bg-white/20 transition-colors">
                        <Icon className="w-5 h-5" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-semibold text-sm">{item.label}</div>
                        <div className="text-xs text-gray-400 group-hover:text-gray-300 transition-colors">
                          {item.description}
                        </div>
                      </div>
                    </NavLink>
                  );
                })}

                {/* External Interview Link */}
                <a
                  href="https://interviewpage-inky.vercel.app/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-4 text-gray-200 hover:bg-white/5 hover:text-white rounded-xl px-4 py-4 transition-all duration-200 group"
                >
                  <div className="flex-shrink-0 w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center group-hover:bg-white/20 transition-colors">
                    <TvMinimalPlayIcon className="w-5 h-5" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-semibold text-sm">Interview</div>
                    <div className="text-xs text-gray-400 group-hover:text-gray-300 transition-colors">
                      Conduct interviews
                    </div>
                  </div>
                  <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
              </nav>

              {/* Sidebar Footer */}
              <div className="mt-auto p-4 border-t border-gray-800">
                <div className="text-center text-xs text-gray-400">
                  &copy; {new Date().getFullYear()} Proyuj Platform
                </div>
              </div>
            </aside>
          </>
        )}

        {/* Enhanced Main Content */}
        <main className="flex-1 overflow-y-auto bg-gray-50">
          {/* Breadcrumb and Page Header */}
          {!hideSidebar && (
            <div className="bg-white border-b border-gray-200 px-6 py-4">
              <div className="flex items-center justify-between">
                <div>
                  {/* Breadcrumbs */}
                  <nav className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                    {pageInfo.breadcrumbs.map((crumb, index) => (
                      <React.Fragment key={crumb}>
                        {index > 0 && <ChevronRight className="w-4 h-4 text-gray-400" />}
                        <span className={index === pageInfo.breadcrumbs.length - 1 ? "text-[rgb(35,65,75)] font-medium" : "hover:text-gray-800 cursor-pointer"}>
                          {crumb}
                        </span>
                      </React.Fragment>
                    ))}
                  </nav>
                  <h1 className="text-2xl font-bold text-gray-900">{pageInfo.title}</h1>
                </div>
              </div>
            </div>
          )}

          {/* Content Area */}
          <div className={`${hideSidebar ? 'p-0' : 'p-6'} min-h-full`}>
            {/* Back button for test page */}
            {location.pathname === "/test" && (
              <div className="mb-6">
                <button
                  onClick={() => navigate("/dashboard")}
                  className="inline-flex items-center gap-2 px-6 py-3 bg-white text-[rgb(35,65,75)] rounded-xl shadow-lg font-semibold border border-gray-100 hover:shadow-xl hover:bg-gray-50 transition-all duration-200"
                >
                  <ArrowLeft className="w-5 h-5" />
                  Back to Dashboard
                </button>
              </div>
            )}

            {/* Page Content */}
            <div className={`${hideSidebar ? '' : 'max-w-7xl mx-auto'}`}>
              <Outlet />
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Layout;
