import React, { useState, useContext } from 'react';
import { motion } from 'framer-motion';
import { SharedDataContext } from '../../App';
import AddCompanyForm from './components/AddCompanyForm'; // Make sure the path is correct

const AdminCompanies = () => {
  const [showForm, setShowForm] = useState(false);
  const [form, setForm] = useState({
    name: '',
    email: '',
    address: '',
    mode: 'Online',
    timing: '',
  });
  const [success, setSuccess] = useState(false);
  const { addCompany } = useContext(SharedDataContext);

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    addCompany(form);
    setSuccess(true);
    setTimeout(() => {
      setShowForm(false);
      setSuccess(false);
      setForm({
        name: '',
        email: '',
        address: '',
        mode: 'Online',
        timing: '',
      });
    }, 1500);
  };

  if (showForm) {
    return (
      <AddCompanyForm
        form={form}
        success={success}
        handleChange={handleChange}
        handleSubmit={handleSubmit}
      />
    );
  }

  return (
    <div className="min-h-screen w-full flex flex-col items-center justify-center bg-gradient-to-br from-gray-50 via-blue-50 to-gray-100 py-12 px-4 relative">
      <motion.div
        className="w-full max-w-xl flex flex-col items-center mb-8"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 px-10 py-8 flex flex-col items-center w-full">
          <h1 className="text-3xl md:text-4xl font-extrabold text-gray-800 dark:text-gray-200 text-center tracking-tight mb-2">
            Add a New Company
          </h1>
          <p className="text-base text-gray-600 dark:text-gray-400 text-center font-medium">
            Fill in the details to add a new company to the directory.
          </p>
        </div>
      </motion.div>

      <motion.button
        className="mb-6 px-8 py-3 text-white rounded-xl font-bold shadow-lg transition-colors duration-200 text-lg focus:outline-none focus:ring-4 focus:ring-blue-300"
        style={{ background: 'rgb(35, 65, 75)' }}
        onClick={() => setShowForm(true)}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        Add Company
      </motion.button>
    </div>
  );
};

export default AdminCompanies; 