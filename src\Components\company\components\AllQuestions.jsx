import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  TrashIcon,
  DocumentArrowDownIcon,
  PlusIcon,
  EyeIcon,
  EyeSlashIcon,
  CheckIcon,
  XMarkIcon,
  Squares2X2Icon,
  TableCellsIcon,
  AdjustmentsHorizontalIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  PencilIcon,
  ClockIcon,
  TagIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import QuestionCard from './QuestionCard';
import QuestionModal from './QuestionModal';
import useQuestionManager from '../hooks/useQuestionManager';
import toast from 'react-hot-toast';

const AllQuestions = ({
  onEditQuestion,
  onDeleteQuestion,
  onDeleteMultiple,
  onExport,
  onAddQuestion
}) => {
  // Use the question manager hook
  const {
    questions,
    loading,
    error,
    pagination,
    statistics,
    loadQuestions
  } = useQuestionManager();

  const [searchTerm, setSearchTerm] = useState('');
  const [showAnswers, setShowAnswers] = useState(false);
  const [selectedQuestions, setSelectedQuestions] = useState(new Set());
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [sortBy, setSortBy] = useState('newest');
  const [filterBy, setFilterBy] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [difficultyFilter, setDifficultyFilter] = useState('all');
  const [viewMode, setViewMode] = useState('cards'); // 'cards' or 'table'
  const [showFilters, setShowFilters] = useState(false);
  const [selectedQuestionForModal, setSelectedQuestionForModal] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(12);
  const [sortOrder, setSortOrder] = useState('desc');

  // Since we're using server-side pagination, questions are already filtered
  const jobQuestions = questions || [];

  // Get unique categories and difficulties for filters (from all questions, not just current page)
  const categories = useMemo(() => {
    // For now, use predefined categories since we need all categories, not just from current page
    return ['Frontend', 'Backend', 'Full Stack', 'Data Science', 'DevOps', 'Mobile', 'UI/UX', 'QA', 'Aptitude', 'Logical', 'Other'];
  }, []);

  const difficulties = useMemo(() => {
    return ['Easy', 'Medium', 'Hard'];
  }, []);

  // Since we're using server-side pagination, questions are already filtered and paginated
  const filteredQuestions = jobQuestions;
  const paginatedQuestions = jobQuestions;
  const totalPages = pagination?.pages || 1;

  // Load questions when filters change
  useEffect(() => {
    const params = {
      page: currentPage,
      limit: itemsPerPage,
      search: searchTerm || undefined,
      category: categoryFilter !== 'all' ? categoryFilter : undefined,
      difficulty: difficultyFilter !== 'all' ? difficultyFilter : undefined,
      questionType: filterBy !== 'all' ? filterBy : undefined,
      sortBy: sortBy === 'newest' ? 'createdAt' : sortBy,
      sortOrder
    };

    loadQuestions(params);
  }, [currentPage, itemsPerPage, searchTerm, categoryFilter, difficultyFilter, filterBy, sortBy, sortOrder, loadQuestions]);

  // Debounced search to avoid too many API calls
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (currentPage !== 1) {
        setCurrentPage(1); // Reset to first page when search changes
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, categoryFilter, difficultyFilter, filterBy]);

  const handleSelectQuestion = (questionId) => {
    if (!isSelectionMode) return;

    const newSelected = new Set(selectedQuestions);
    if (newSelected.has(questionId)) {
      newSelected.delete(questionId);
    } else {
      newSelected.add(questionId);
    }
    setSelectedQuestions(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedQuestions.size === paginatedQuestions.length) {
      setSelectedQuestions(new Set());
    } else {
      setSelectedQuestions(new Set(paginatedQuestions.map(q => q._id || q.id)));
    }
  };

  const handleDeleteSelected = () => {
    if (selectedQuestions.size === 0) return;

    const questionIds = Array.from(selectedQuestions);
    onDeleteMultiple?.(questionIds);
    setSelectedQuestions(new Set());
    setIsSelectionMode(false);
    toast.success(`Deleted ${questionIds.length} question(s)`);
  };

  const handleExport = () => {
    onExport?.();
    toast.success('Questions exported successfully');
  };

  const toggleSelectionMode = () => {
    setIsSelectionMode(!isSelectionMode);
    setSelectedQuestions(new Set());
  };

  const handleViewQuestion = (question) => {
    setSelectedQuestionForModal(question);
  };

  const closeQuestionModal = () => {
    setSelectedQuestionForModal(null);
  };

  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const clearAllFilters = () => {
    setSearchTerm('');
    setFilterBy('all');
    setCategoryFilter('all');
    setDifficultyFilter('all');
    setCurrentPage(1);
  };

  const getQuestionStats = () => {
    return {
      total: statistics?.totalQuestions || 0,
      filtered: pagination?.total || 0,
      byType: {},
      byCategory: {},
      byDifficulty: {}
    };
  };

  const stats = getQuestionStats();

  return (
    <div className="w-full max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-3xl font-bold text-gray-800">
              All Questions
            </h2>
            <div className="flex items-center gap-4 mt-2">
              <p className="text-gray-600">
                {pagination?.total || 0} of {statistics?.totalQuestions || 0} questions
                {pagination?.total !== statistics?.totalQuestions && (
                  <span className="text-blue-600 ml-1">(filtered)</span>
                )}
              </p>
              {(searchTerm || categoryFilter !== 'all' || difficultyFilter !== 'all' || filterBy !== 'all') && (
                <button
                  onClick={clearAllFilters}
                  className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                >
                  Clear filters
                </button>
              )}
            </div>
          </div>
          <div className="flex items-center gap-3">
            {/* View Mode Toggle */}
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('cards')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'cards'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
                title="Card View"
              >
                <Squares2X2Icon className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('table')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'table'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
                title="Table View"
              >
                <TableCellsIcon className="h-4 w-4" />
              </button>
            </div>

            <button
              onClick={onAddQuestion}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <PlusIcon className="h-4 w-4" />
              Add Question
            </button>
          </div>
        </div>

        {/* Search and Quick Actions */}
        <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between mb-4">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search questions, answers, explanations..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Quick Actions */}
          <div className="flex items-center gap-2">
            {/* Advanced Filters Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
                showFilters
                  ? 'bg-blue-100 text-blue-600'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              <AdjustmentsHorizontalIcon className="h-4 w-4" />
              Filters
            </button>

            {/* Show Answers Toggle */}
            <button
              onClick={() => setShowAnswers(!showAnswers)}
              className={`p-2 rounded-lg transition-colors ${
                showAnswers
                  ? 'bg-green-100 text-green-600'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
              title={showAnswers ? 'Hide Answers' : 'Show Answers'}
            >
              {showAnswers ? <EyeSlashIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
            </button>

            {/* Selection Mode Toggle */}
            <button
              onClick={toggleSelectionMode}
              className={`p-2 rounded-lg transition-colors ${
                isSelectionMode
                  ? 'bg-blue-100 text-blue-600'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
              title="Selection Mode"
            >
              <CheckIcon className="h-4 w-4" />
            </button>

            {/* Export */}
            <button
              onClick={handleExport}
              className="p-2 bg-gray-100 text-gray-600 hover:bg-gray-200 rounded-lg transition-colors"
              title="Export Questions"
            >
              <DocumentArrowDownIcon className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Advanced Filters */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mb-4 p-4 bg-gray-50 rounded-lg border border-gray-200"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Sort */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="newest">Newest First</option>
                    <option value="oldest">Oldest First</option>
                    <option value="questionText">Question Text</option>
                    <option value="category">Category</option>
                    <option value="difficulty">Difficulty</option>
                    <option value="points">Points</option>
                  </select>
                </div>

                {/* Question Type Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Question Type</label>
                  <select
                    value={filterBy}
                    onChange={(e) => setFilterBy(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Types</option>
                    <option value="MCQ">MCQ</option>
                    <option value="Multiple-Select">Multiple-Select</option>
                    <option value="Short-Answer">Short-Answer</option>
                    <option value="Code">Code</option>
                  </select>
                </div>

                {/* Category Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                  <select
                    value={categoryFilter}
                    onChange={(e) => setCategoryFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Categories</option>
                    {categories.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                </div>

                {/* Difficulty Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Difficulty</label>
                  <select
                    value={difficultyFilter}
                    onChange={(e) => setDifficultyFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Difficulties</option>
                    {difficulties.map(difficulty => (
                      <option key={difficulty} value={difficulty}>{difficulty}</option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Filter Stats */}
              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    <TagIcon className="h-4 w-4" />
                    <span>Total Questions: {statistics?.totalQuestions || 0}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <StarIcon className="h-4 w-4" />
                    <span>Showing: {pagination?.total || 0}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <ClockIcon className="h-4 w-4" />
                    <span>Page {pagination?.current || 1} of {pagination?.pages || 1}</span>
                  </div>
                  {statistics?.averagePoints && (
                    <div className="flex items-center gap-2">
                      <span>Avg Points: {statistics.averagePoints.toFixed(1)}</span>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Selection Mode Controls */}
        <AnimatePresence>
          {isSelectionMode && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <button
                    onClick={handleSelectAll}
                    className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                  >
                    {selectedQuestions.size === paginatedQuestions.length && paginatedQuestions.length > 0 ? 'Deselect All' : 'Select All'}
                  </button>
                  <span className="text-sm text-gray-600">
                    {selectedQuestions.size} selected
                  </span>
                  {selectedQuestions.size > 0 && (
                    <span className="text-xs text-gray-500">
                      (from current page)
                    </span>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={handleDeleteSelected}
                    disabled={selectedQuestions.size === 0}
                    className="flex items-center gap-1 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <TrashIcon className="h-3 w-3" />
                    Delete Selected
                  </button>
                  <button
                    onClick={toggleSelectionMode}
                    className="p-1 text-gray-500 hover:text-gray-700"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Questions Display */}
      {loading && filteredQuestions.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-64 text-gray-500">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
          <h3 className="text-xl font-semibold mb-2">Loading Questions...</h3>
          <p className="text-center">Please wait while we fetch your questions.</p>
        </div>
      ) : filteredQuestions.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-64 text-gray-500">
          <div className="text-6xl mb-4">🤔</div>
          <h3 className="text-xl font-semibold mb-2">No Questions Found</h3>
          <p className="text-center">
            {searchTerm || categoryFilter !== 'all' || difficultyFilter !== 'all' || filterBy !== 'all'
              ? 'Try adjusting your search terms or filters.'
              : 'Start by adding some questions.'
            }
          </p>
        </div>
      ) : viewMode === 'table' ? (
        /* Table View */
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  {isSelectionMode && (
                    <th className="w-12 px-4 py-3">
                      <input
                        type="checkbox"
                        checked={selectedQuestions.size === paginatedQuestions.length && paginatedQuestions.length > 0}
                        onChange={handleSelectAll}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </th>
                  )}
                  <th className="text-left px-4 py-3 text-sm font-semibold text-gray-900">
                    <button
                      onClick={() => handleSort('questionText')}
                      className="flex items-center gap-1 hover:text-blue-600"
                    >
                      Question
                      {sortBy === 'questionText' && (
                        sortOrder === 'asc' ? <ChevronUpIcon className="h-4 w-4" /> : <ChevronDownIcon className="h-4 w-4" />
                      )}
                    </button>
                  </th>
                  <th className="text-left px-4 py-3 text-sm font-semibold text-gray-900">
                    <button
                      onClick={() => handleSort('category')}
                      className="flex items-center gap-1 hover:text-blue-600"
                    >
                      Category
                      {sortBy === 'category' && (
                        sortOrder === 'asc' ? <ChevronUpIcon className="h-4 w-4" /> : <ChevronDownIcon className="h-4 w-4" />
                      )}
                    </button>
                  </th>
                  <th className="text-left px-4 py-3 text-sm font-semibold text-gray-900">Type</th>
                  <th className="text-left px-4 py-3 text-sm font-semibold text-gray-900">
                    <button
                      onClick={() => handleSort('difficulty')}
                      className="flex items-center gap-1 hover:text-blue-600"
                    >
                      Difficulty
                      {sortBy === 'difficulty' && (
                        sortOrder === 'asc' ? <ChevronUpIcon className="h-4 w-4" /> : <ChevronDownIcon className="h-4 w-4" />
                      )}
                    </button>
                  </th>
                  <th className="text-left px-4 py-3 text-sm font-semibold text-gray-900">
                    <button
                      onClick={() => handleSort('points')}
                      className="flex items-center gap-1 hover:text-blue-600"
                    >
                      Points
                      {sortBy === 'points' && (
                        sortOrder === 'asc' ? <ChevronUpIcon className="h-4 w-4" /> : <ChevronDownIcon className="h-4 w-4" />
                      )}
                    </button>
                  </th>
                  <th className="text-left px-4 py-3 text-sm font-semibold text-gray-900">
                    <button
                      onClick={() => handleSort('createdAt')}
                      className="flex items-center gap-1 hover:text-blue-600"
                    >
                      Created
                      {sortBy === 'createdAt' && (
                        sortOrder === 'asc' ? <ChevronUpIcon className="h-4 w-4" /> : <ChevronDownIcon className="h-4 w-4" />
                      )}
                    </button>
                  </th>
                  {!isSelectionMode && (
                    <th className="text-right px-4 py-3 text-sm font-semibold text-gray-900">Actions</th>
                  )}
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {paginatedQuestions.map((question, index) => (
                  <motion.tr
                    key={question._id || question.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: index * 0.05 }}
                    className={`hover:bg-gray-50 ${
                      selectedQuestions.has(question._id || question.id) ? 'bg-blue-50' : ''
                    }`}
                  >
                    {isSelectionMode && (
                      <td className="px-4 py-3">
                        <input
                          type="checkbox"
                          checked={selectedQuestions.has(question._id || question.id)}
                          onChange={() => handleSelectQuestion(question._id || question.id)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </td>
                    )}
                    <td className="px-4 py-3">
                      <div className="max-w-xs">
                        <p className="text-sm font-medium text-gray-900 line-clamp-2">
                          {question.questionText || question.question}
                        </p>
                        {showAnswers && question.correctAnswer && (
                          <p className="text-xs text-green-600 mt-1 line-clamp-1">
                            Answer: {question.correctAnswer}
                          </p>
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-3">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {question.category || 'Uncategorized'}
                      </span>
                    </td>
                    <td className="px-4 py-3">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        {question.questionType || question.type || 'MCQ'}
                      </span>
                    </td>
                    <td className="px-4 py-3">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        question.difficulty === 'Easy' ? 'bg-green-100 text-green-800' :
                        question.difficulty === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                        question.difficulty === 'Hard' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {question.difficulty || 'Medium'}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">
                      {question.points || 1}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-500">
                      {question.createdAt ? new Date(question.createdAt).toLocaleDateString() : '-'}
                    </td>
                    {!isSelectionMode && (
                      <td className="px-4 py-3 text-right">
                        <div className="flex items-center justify-end gap-2">
                          <button
                            onClick={() => handleViewQuestion(question)}
                            className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                            title="View Details"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => onEditQuestion(question)}
                            className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                            title="Edit Question"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => onDeleteQuestion(question._id || question.id)}
                            className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                            title="Delete Question"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    )}
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ) : (
        /* Card View */
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {paginatedQuestions.map((question, index) => (
            <motion.div
              key={question._id || question.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
            >
              <QuestionCard
                question={question}
                onEdit={onEditQuestion}
                onDelete={onDeleteQuestion}
                onView={handleViewQuestion}
                showAnswer={showAnswers}
                isSelected={selectedQuestions.has(question._id || question.id)}
                onSelect={isSelectionMode ? handleSelectQuestion : null}
                showActions={!isSelectionMode}
              />
            </motion.div>
          ))}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="mt-8 flex items-center justify-between">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <span>Show</span>
            <select
              value={itemsPerPage}
              onChange={(e) => {
                setItemsPerPage(Number(e.target.value));
                setCurrentPage(1);
              }}
              className="px-2 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500"
            >
              <option value={6}>6</option>
              <option value={12}>12</option>
              <option value={24}>24</option>
              <option value={48}>48</option>
            </select>
            <span>per page</span>
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1 || loading}
              className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>

            <div className="flex items-center gap-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let page;
                if (totalPages <= 5) {
                  page = i + 1;
                } else {
                  // Show pages around current page
                  const start = Math.max(1, currentPage - 2);
                  page = start + i;
                  if (page > totalPages) return null;
                }

                return (
                  <button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    disabled={loading}
                    className={`px-3 py-1 text-sm rounded disabled:opacity-50 ${
                      currentPage === page
                        ? 'bg-blue-600 text-white'
                        : 'border border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {page}
                  </button>
                );
              })}
              {totalPages > 5 && currentPage < totalPages - 2 && (
                <>
                  <span className="px-2 text-gray-500">...</span>
                  <button
                    onClick={() => setCurrentPage(totalPages)}
                    disabled={loading}
                    className={`px-3 py-1 text-sm rounded disabled:opacity-50 ${
                      currentPage === totalPages
                        ? 'bg-blue-600 text-white'
                        : 'border border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {totalPages}
                  </button>
                </>
              )}
            </div>

            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages || loading}
              className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>

          <div className="text-sm text-gray-600">
            Page {currentPage} of {totalPages}
            {loading && <span className="ml-2 text-blue-600">Loading...</span>}
          </div>
        </div>
      )}

      {/* Question Detail Modal */}
      <QuestionModal
        isOpen={!!selectedQuestionForModal}
        onClose={closeQuestionModal}
        question={selectedQuestionForModal}
        onEdit={onEditQuestion}
        onDelete={onDeleteQuestion}
      />
    </div>
  );
};

export default AllQuestions;
