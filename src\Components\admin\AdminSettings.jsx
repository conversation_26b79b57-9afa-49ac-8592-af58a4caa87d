import React, { useContext, useState, useEffect } from 'react';
import { SharedDataContext } from '../../App';
import { motion } from 'framer-motion';
import { Sun, Moon, Trash2, User, Info } from 'lucide-react';

const AdminSettings = () => {
  const { setCompanies, setJobPosts, theme, setTheme } = useContext(SharedDataContext);
  const [showConfirm, setShowConfirm] = useState(null);
  const [profile, setProfile] = useState(null);

  useEffect(() => {
    const storedProfile = localStorage.getItem('adminUserProfile');
    if (storedProfile) {
      setProfile(JSON.parse(storedProfile));
    }
  }, []);

  const handleClearData = (dataType) => {
    if (dataType === 'companies') {
      setCompanies([]);
    } else if (dataType === 'jobPosts') {
      setJobPosts([]);
    }
    setShowConfirm(null);
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
  };

  return (
    <div className="p-4 sm:p-6">
      <h1 className="text-3xl font-extrabold text-[rgb(35,65,75)] mb-8 tracking-tight">Settings</h1>

      <motion.div 
        className="grid grid-cols-1 lg:grid-cols-3 gap-8"
        initial="hidden"
        animate="visible"
        variants={{ visible: { transition: { staggerChildren: 0.1 } } }}
      >
        {/* User Profile Card (takes up 1 column) */}
        <motion.div className="lg:col-span-1 bg-white rounded-xl shadow-lg p-6 flex flex-col items-center text-center" variants={cardVariants}>
          <div className="w-24 h-24 rounded-full bg-[rgb(35,65,75)] flex items-center justify-center text-white font-bold text-4xl mb-4 shadow-md">
            {profile ? profile.name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0,2) : <User size={40} />}
          </div>
          <h2 className="text-2xl font-bold text-gray-800">{profile ? profile.name : 'Admin User'}</h2>
          <p className="text-gray-600">{profile ? profile.email : 'No email set'}</p>
          <span className="mt-2 px-3 py-1 bg-blue-100 text-blue-800 text-sm font-semibold rounded-full">{profile ? profile.role : 'Admin'}</span>
        </motion.div>

        {/* Settings Cards (takes up 2 columns) */}
        <div className="lg:col-span-2 grid grid-cols-1 gap-8">
          {/* Appearance Card */}
          <motion.div className="bg-white rounded-xl shadow-lg p-6" variants={cardVariants}>
            <h2 className="text-xl font-bold text-gray-800 mb-4">Appearance</h2>
            <div className="flex items-center justify-between">
              <span className="font-semibold text-gray-600">Theme</span>
              <div className="flex items-center gap-2 bg-gray-100 rounded-full p-1">
                <button 
                  onClick={() => setTheme('light')}
                  className={`p-2 rounded-full transition-colors ${theme === 'light' ? 'bg-white shadow' : 'hover:bg-gray-200'}`}
                  title="Light Mode"
                >
                  <Sun className="w-5 h-5 text-gray-700" />
                </button>
                <button 
                  onClick={() => setTheme('dark')}
                  className={`p-2 rounded-full transition-colors ${theme === 'dark' ? 'bg-[rgb(35,65,75)] text-white shadow' : 'hover:bg-gray-200'}`}
                  title="Dark Mode"
                >
                  <Moon className="w-5 h-5" />
                </button>
              </div>
            </div>
          </motion.div>

          {/* Data Management Card */}
          <motion.div className="bg-white rounded-xl shadow-lg p-6" variants={cardVariants}>
            <h2 className="text-xl font-bold text-gray-800 mb-4">Data Management</h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="font-semibold text-gray-600">Clear All Companies</span>
                <button
                  onClick={() => setShowConfirm('companies')}
                  className="flex items-center gap-2 px-4 py-2 bg-red-100 text-red-700 rounded-lg font-semibold hover:bg-red-200 transition-colors"
                >
                  <Trash2 className="w-5 h-5" />
                  Clear
                </button>
              </div>
              <div className="flex items-center justify-between">
                <span className="font-semibold text-gray-600">Clear All Job Posts</span>
                <button
                  onClick={() => setShowConfirm('jobPosts')}
                  className="flex items-center gap-2 px-4 py-2 bg-red-100 text-red-700 rounded-lg font-semibold hover:bg-red-200 transition-colors"
                >
                  <Trash2 className="w-5 h-5" />
                  Clear
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      </motion.div>

      {/* Confirmation Modal (remains the same) */}
      {showConfirm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <motion.div
            className="bg-white rounded-xl shadow-2xl p-8 w-full max-w-md text-center"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
          >
            <h3 className="text-xl font-bold mb-4">Are you sure?</h3>
            <p className="text-gray-600 mb-6">This will permanently delete all {showConfirm}. This action cannot be undone.</p>
            <div className="flex justify-center gap-4">
              <button onClick={() => setShowConfirm(null)} className="px-6 py-2 bg-gray-200 rounded-lg font-semibold hover:bg-gray-300 transition">Cancel</button>
              <button onClick={() => handleClearData(showConfirm)} className="px-6 py-2 bg-red-600 text-white rounded-lg font-semibold hover:bg-red-700 transition">Confirm</button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default AdminSettings; 