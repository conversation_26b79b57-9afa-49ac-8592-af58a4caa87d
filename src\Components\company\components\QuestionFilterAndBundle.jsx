import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  XMarkIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  CheckIcon,
  BookmarkIcon,
  DocumentTextIcon,
  TagIcon,
  AcademicCapIcon,
  ClockIcon,
  PlusIcon,
  ChevronDownIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';
import useCompanyStore from '../../../store/companyStore';
import toast from 'react-hot-toast';

const QuestionFilterAndBundle = ({ 
  isOpen, 
  onClose, 
  onAddToTest,
  existingQuestions = []
}) => {
  const { 
    questions,
    questionCategories,
    questionBundles,
    getQuestions,
    getQuestionCategories,
    getQuestionBundles,
    filterQuestions,
    createQuestionBundle,
    loading 
  } = useCompanyStore();

  // State for filtering
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [selectedDifficulty, setSelectedDifficulty] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [filteredQuestions, setFilteredQuestions] = useState([]);
  
  // State for question selection
  const [selectedQuestions, setSelectedQuestions] = useState([]);
  const [categoryExpanded, setCategoryExpanded] = useState({});
  
  // State for bundling
  const [showCreateBundle, setShowCreateBundle] = useState(false);
  const [bundleName, setBundleName] = useState('');
  const [bundleDescription, setBundleDescription] = useState('');
  
  // Active tab
  const [activeTab, setActiveTab] = useState('filter'); // 'filter', 'bundles'

  // Available categories, difficulties, and types
  const difficulties = ['Easy', 'Medium', 'Hard'];
  const questionTypes = ['MCQ', 'Code', 'Short Answer', 'True/False'];

  useEffect(() => {
    if (isOpen) {
      initializeData();
    }
  }, [isOpen]);

  useEffect(() => {
    handleFilter();
  }, [questions, searchTerm, selectedCategories, selectedDifficulty, selectedType]);

  const initializeData = async () => {
    await Promise.all([
      getQuestions(),
      getQuestionCategories(),
      getQuestionBundles()
    ]);
  };

  const handleFilter = async () => {
    const filters = {
      searchTerm,
      category: selectedCategories.length > 0 ? selectedCategories[0] : '',
      difficulty: selectedDifficulty,
      type: selectedType
    };

    if (Object.values(filters).some(f => f)) {
      const result = await filterQuestions(filters);
      // Handle both old format (array) and new format (object with questions property)
      const filtered = Array.isArray(result) ? result : (result?.questions || []);
      setFilteredQuestions(filtered);
    } else {
      setFilteredQuestions(questions);
    }
  };

  // Group questions by category
  const questionsByCategory = filteredQuestions.reduce((acc, question) => {
    const category = question.category || 'Uncategorized';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(question);
    return acc;
  }, {});

  const handleCategoryToggle = (category) => {
    setSelectedCategories(prev => 
      prev.includes(category) 
        ? prev.filter(c => c !== category)
        : [category] // Only allow one category at a time for simplicity
    );
  };

  const handleCategoryExpand = (category) => {
    setCategoryExpanded(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  };

  const handleQuestionSelect = (question) => {
    setSelectedQuestions(prev => {
      const isSelected = prev.some(q => q._id === question._id);
      if (isSelected) {
        return prev.filter(q => q._id !== question._id);
      } else {
        return [...prev, { ...question, points: 1 }];
      }
    });
  };

  const handleSelectAllInCategory = (category) => {
    const categoryQuestions = questionsByCategory[category] || [];
    const allSelected = categoryQuestions.every(q => 
      selectedQuestions.some(sq => sq._id === q._id)
    );

    if (allSelected) {
      // Deselect all in category
      setSelectedQuestions(prev => 
        prev.filter(sq => !categoryQuestions.some(cq => cq._id === sq._id))
      );
    } else {
      // Select all in category
      const newSelections = categoryQuestions
        .filter(q => !selectedQuestions.some(sq => sq._id === q._id))
        .filter(q => !existingQuestions.some(eq => eq.questionId === q._id))
        .map(q => ({ ...q, points: 1 }));
      setSelectedQuestions(prev => [...prev, ...newSelections]);
    }
  };

  const updateQuestionPoints = (questionId, points) => {
    setSelectedQuestions(prev =>
      prev.map(q => q._id === questionId ? { ...q, points } : q)
    );
  };

  const handleCreateBundle = async () => {
    if (!bundleName.trim()) {
      toast.error('Please enter a bundle name');
      return;
    }
    
    if (selectedQuestions.length === 0) {
      toast.error('Please select at least one question');
      return;
    }

    const categories = [...new Set(selectedQuestions.map(q => q.category).filter(Boolean))];
    const difficulties = [...new Set(selectedQuestions.map(q => q.difficulty).filter(Boolean))];
    const allTags = selectedQuestions.flatMap(q => q.tags || []);
    const uniqueTags = [...new Set(allTags.filter(Boolean))];

    const bundleData = {
      bundleName: bundleName,
      description: bundleDescription,
      category: categories.length > 0 ? categories[0] : 'General', // Use first category as primary
      difficulty: difficulties.length > 0 ? difficulties[0] : 'Medium', // Use first difficulty as primary
      questionIds: selectedQuestions.map(q => q._id),
      tags: uniqueTags.length > 0 ? uniqueTags : categories // Use tags if available, otherwise use categories
    };

    const result = await createQuestionBundle(bundleData);
    if (result) {
      toast.success('Question bundle created successfully');
      setBundleName('');
      setBundleDescription('');
      setSelectedQuestions([]);
      setShowCreateBundle(false);
      await getQuestionBundles();
    }
  };

  const handleAddBundleToTest = (bundle) => {
    // Handle both old format (with questions array) and new format (with questionIds array)
    let questionsToAdd = [];

    if (bundle.questions && Array.isArray(bundle.questions)) {
      // Old format: questions array with questionId and points
      questionsToAdd = bundle.questions.map(q => ({
        questionId: q.questionId,
        points: q.points || 1,
        optionsSnapshot: []
      }));
    } else if (bundle.questionIds && Array.isArray(bundle.questionIds)) {
      // New format: questionIds array
      questionsToAdd = bundle.questionIds.map(questionId => ({
        questionId: questionId,
        points: 1, // Default points
        optionsSnapshot: []
      }));
    }

    onAddToTest(questionsToAdd);
    const bundleName = bundle.bundleName || bundle.name || 'Bundle';
    const questionCount = questionsToAdd.length;
    toast.success(`Added ${questionCount} questions from "${bundleName}" to test`);
  };

  const handleAddSelectedToTest = () => {
    if (selectedQuestions.length === 0) {
      toast.error('Please select at least one question');
      return;
    }

    const questionsToAdd = selectedQuestions.map(q => ({
      questionId: q._id,
      points: q.points || 1,
      optionsSnapshot: []
    }));
    
    onAddToTest(questionsToAdd);
    toast.success(`Added ${selectedQuestions.length} questions to test`);
    setSelectedQuestions([]);
  };

  const clearAllFilters = () => {
    setSearchTerm('');
    setSelectedCategories([]);
    setSelectedDifficulty('');
    setSelectedType('');
  };

  const isQuestionSelected = (question) => {
    return selectedQuestions.some(q => q._id === question._id);
  };

  const isQuestionInTest = (question) => {
    return existingQuestions.some(eq => eq.questionId === question._id);
  };

  const availableQuestions = filteredQuestions.filter(q => !isQuestionInTest(q));

  const renderFilterTab = () => (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="space-y-4">
        <div className="relative">
          <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search questions by text, category, or tags..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <select
            value={selectedDifficulty}
            onChange={(e) => setSelectedDifficulty(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Difficulties</option>
            {difficulties.map(difficulty => (
              <option key={difficulty} value={difficulty}>{difficulty}</option>
            ))}
          </select>

          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Types</option>
            {questionTypes.map(type => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>

          <button
            onClick={clearAllFilters}
            className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors"
          >
            Clear Filters
          </button>
        </div>

        {/* Category Filter */}
        <div>
          <h4 className="font-medium text-gray-800 mb-3">Filter by Categories:</h4>
          <div className="flex flex-wrap gap-2">
            {(questionCategories || []).map((categoryItem, index) => {
              // Handle both old format (string) and new format (object with category and count)
              const categoryName = typeof categoryItem === 'string' ? categoryItem : categoryItem.category;
              const categoryCount = typeof categoryItem === 'object' ? categoryItem.count : null;

              return (
                <button
                  key={`category-${index}-${categoryName}`}
                  onClick={() => handleCategoryToggle(categoryName)}
                  className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                    selectedCategories.includes(categoryName)
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {categoryName}
                  {categoryCount !== null && (
                    <span className="ml-1 text-xs opacity-75">({categoryCount})</span>
                  )}
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Selection Summary */}
      <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
        <div className="flex items-center gap-4">
          <span className="text-sm text-gray-600">
            {availableQuestions.length} questions found
          </span>
          <span className="text-sm font-medium text-blue-600">
            {selectedQuestions.length} selected
          </span>
          <span className="text-sm text-gray-600">
            Total points: {selectedQuestions.reduce((sum, q) => sum + (q.points || 1), 0)}
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          {selectedQuestions.length > 0 && (
            <button
              onClick={() => setShowCreateBundle(true)}
              className="flex items-center gap-1 px-3 py-1 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700 transition-colors"
            >
              <BookmarkIcon className="h-4 w-4" />
              Save as Bundle
            </button>
          )}
        </div>
      </div>

      {/* Questions by Category */}
      <div className="space-y-4 max-h-96 overflow-y-auto">
        {Object.keys(questionsByCategory).length === 0 ? (
          <div className="text-center py-12">
            <DocumentTextIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-600 mb-2">No Questions Found</h3>
            <p className="text-gray-500">Try adjusting your filters to see more questions</p>
          </div>
        ) : (
          Object.entries(questionsByCategory).map(([category, categoryQuestions], categoryIndex) => {
            const isExpanded = categoryExpanded[category] !== false; // Default to expanded
            const selectedInCategory = (categoryQuestions || []).filter(q =>
              selectedQuestions.some(sq => sq._id === q._id)
            ).length;

            return (
              <div key={`category-section-${categoryIndex}-${category}`} className="border border-gray-200 rounded-lg overflow-hidden">
                <div className="bg-gray-50 p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <button
                        onClick={() => handleCategoryExpand(category)}
                        className="p-1 hover:bg-gray-200 rounded transition-colors"
                      >
                        {isExpanded ? (
                          <ChevronDownIcon className="h-5 w-5 text-gray-600" />
                        ) : (
                          <ChevronRightIcon className="h-5 w-5 text-gray-600" />
                        )}
                      </button>
                      <TagIcon className="h-5 w-5 text-blue-600" />
                      <h4 className="font-semibold text-gray-800">{category}</h4>
                      <span className="text-sm text-gray-600">
                        ({categoryQuestions.length} questions)
                      </span>
                      {selectedInCategory > 0 && (
                        <span className="text-sm font-medium text-blue-600">
                          {selectedInCategory} selected
                        </span>
                      )}
                    </div>
                    
                    <button
                      onClick={() => handleSelectAllInCategory(category)}
                      className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                    >
                      {selectedInCategory === categoryQuestions.length ? 'Deselect All' : 'Select All'}
                    </button>
                  </div>
                </div>

                {isExpanded && (
                  <div className="p-4 space-y-3">
                    {(categoryQuestions || []).map((question, index) => {
                      const isSelected = isQuestionSelected(question);
                      const selectedQuestion = selectedQuestions.find(q => q._id === question._id);
                      const inTest = isQuestionInTest(question);

                      return (
                        <motion.div
                          key={`${category}-question-${question._id || question.id || `${category}-${index}`}`}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.02 }}
                          className={`p-3 border rounded-lg transition-all ${
                            inTest
                              ? 'border-gray-300 bg-gray-100 opacity-50'
                              : isSelected
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-blue-300'
                          }`}
                        >
                          <div className="flex items-start gap-3">
                            <input
                              type="checkbox"
                              checked={isSelected}
                              disabled={inTest}
                              onChange={() => handleQuestionSelect(question)}
                              className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50"
                            />
                            
                            <div className="flex-1">
                              <div className="font-medium text-gray-800 mb-2">
                                {question.questionText}
                                {inTest && <span className="ml-2 text-xs text-gray-500">(Already in test)</span>}
                              </div>
                              
                              <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                                <div className="flex items-center gap-1">
                                  <AcademicCapIcon className="h-4 w-4" />
                                  {question.difficulty || 'Medium'}
                                </div>
                                <div className="flex items-center gap-1">
                                  <DocumentTextIcon className="h-4 w-4" />
                                  {question.type || 'MCQ'}
                                </div>
                              </div>
                              
                              {isSelected && !inTest && (
                                <div className="mt-3 flex items-center gap-2">
                                  <label className="text-sm font-medium text-gray-700">Points:</label>
                                  <input
                                    type="number"
                                    min="1"
                                    max="10"
                                    value={selectedQuestion?.points || 1}
                                    onChange={(e) => updateQuestionPoints(question._id, parseInt(e.target.value) || 1)}
                                    className="w-16 px-2 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                  />
                                </div>
                              )}
                            </div>
                          </div>
                        </motion.div>
                      );
                    })}
                  </div>
                )}
              </div>
            );
          })
        )}
      </div>
    </div>
  );

  const renderBundlesTab = () => (
    <div className="space-y-4">
      {(!questionBundles || questionBundles.length === 0) ? (
        <div className="text-center py-12">
          <BookmarkIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-600 mb-2">No Question Bundles</h3>
          <p className="text-gray-500">Create your first question bundle to reuse across tests</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {(questionBundles || []).map((bundle, index) => (
            <motion.div
              key={bundle._id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between mb-3">
                <h4 className="font-semibold text-gray-800">{bundle.bundleName || bundle.name}</h4>
                <button
                  onClick={() => handleAddBundleToTest(bundle)}
                  className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <PlusIcon className="h-4 w-4" />
                  Add to Test
                </button>
              </div>

              {bundle.description && (
                <p className="text-gray-600 text-sm mb-3">{bundle.description}</p>
              )}

              <div className="flex items-center gap-4 text-sm text-gray-500 mb-2">
                <span>
                  {bundle.totalQuestions || bundle.questions?.length || bundle.questionIds?.length || 0} questions
                </span>
                <span>{bundle.totalPoints || 0} points</span>
                {bundle.difficulty && (
                  <span className="capitalize">{bundle.difficulty}</span>
                )}
              </div>

              {/* Display categories and tags */}
              {((bundle.categories && bundle.categories.length > 0) || bundle.category || (bundle.tags && bundle.tags.length > 0)) && (
                <div className="flex flex-wrap gap-1">
                  {/* Show primary category */}
                  {bundle.category && (
                    <span className="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded-full font-medium">
                      {bundle.category}
                    </span>
                  )}

                  {/* Show additional categories */}
                  {(bundle.categories || []).map((category, catIndex) => (
                    <span
                      key={`bundle-${bundle._id}-category-${catIndex}-${category}`}
                      className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full"
                    >
                      {category}
                    </span>
                  ))}

                  {/* Show tags */}
                  {(bundle.tags || []).slice(0, 3).map((tag, tagIndex) => (
                    <span
                      key={`bundle-${bundle._id}-tag-${tagIndex}-${tag}`}
                      className="px-2 py-1 bg-green-100 text-green-600 text-xs rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )}
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-white rounded-2xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden"
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold mb-2">Question Filter & Bundle</h2>
                <p className="text-blue-100">Filter questions by category and create reusable bundles</p>
              </div>
              <button
                onClick={onClose}
                className="p-2 hover:bg-white/20 rounded-lg transition-colors"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            {/* Tabs */}
            <div className="flex gap-4 mt-6">
              <button
                onClick={() => setActiveTab('filter')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === 'filter'
                    ? 'bg-white text-blue-700'
                    : 'text-blue-100 hover:text-white hover:bg-white/20'
                }`}
              >
                Filter by Category
              </button>
              <button
                onClick={() => setActiveTab('bundles')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === 'bundles'
                    ? 'bg-white text-blue-700'
                    : 'text-blue-100 hover:text-white hover:bg-white/20'
                }`}
              >
                Saved Bundles
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[60vh]">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent"></div>
                <span className="ml-3 text-gray-600">Loading...</span>
              </div>
            ) : (
              activeTab === 'filter' ? renderFilterTab() : renderBundlesTab()
            )}
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-6 py-4 flex items-center justify-between border-t border-gray-200">
            <div className="text-sm text-gray-600">
              {activeTab === 'filter' && `${selectedQuestions.length} question(s) selected`}
            </div>
            
            <div className="flex items-center gap-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-200 rounded-lg transition-colors"
              >
                Cancel
              </button>
              
              {activeTab === 'filter' && selectedQuestions.length > 0 && (
                <button
                  onClick={handleAddSelectedToTest}
                  className="flex items-center gap-2 px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium"
                >
                  <PlusIcon className="h-4 w-4" />
                  Add {selectedQuestions.length} Question(s) to Test
                </button>
              )}
            </div>
          </div>
        </motion.div>
      </div>

      {/* Create Bundle Modal */}
      {showCreateBundle && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60 p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-xl shadow-xl w-full max-w-md p-6"
          >
            <h3 className="text-lg font-bold text-gray-800 mb-4">Create Question Bundle</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Bundle Name</label>
                <input
                  type="text"
                  value={bundleName}
                  onChange={(e) => setBundleName(e.target.value)}
                  placeholder="e.g., JavaScript Fundamentals"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Description (Optional)</label>
                <textarea
                  value={bundleDescription}
                  onChange={(e) => setBundleDescription(e.target.value)}
                  placeholder="Brief description of this question bundle"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                />
              </div>
              
              <div className="text-sm text-gray-600">
                <div className="flex justify-between">
                  <span>Questions:</span>
                  <span className="font-medium">{selectedQuestions.length}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total Points:</span>
                  <span className="font-medium">{selectedQuestions.reduce((sum, q) => sum + (q.points || 1), 0)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Categories:</span>
                  <span className="font-medium">
                    {[...new Set(selectedQuestions.map(q => q.category).filter(Boolean))].length}
                  </span>
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-3 mt-6">
              <button
                onClick={() => setShowCreateBundle(false)}
                className="flex-1 px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-200 rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateBundle}
                className="flex-1 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors font-medium"
              >
                Create Bundle
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default QuestionFilterAndBundle;
